#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导入分析模块
支持导入Excel和JSON文件，重新分析历史数据，支持批量导入
"""

import os
import json
import pandas as pd
import logging
from typing import List, Dict, Any, Optional
from tkinter import messagebox

class DataImporter:
    """数据导入器"""
    
    def __init__(self, config_manager, score_calculator):
        self.config_manager = config_manager
        self.score_calculator = score_calculator
        self.logger = logging.getLogger(__name__)
    
    def import_excel_file(self, file_path: str) -> Optional[List[Dict[str, Any]]]:
        """导入Excel文件"""
        try:
            self.logger.info(f"开始导入Excel文件: {file_path}")
            
            # 读取Excel文件
            df = pd.read_excel(file_path)
            
            # 转换为字典列表
            data_list = df.to_dict('records')
            
            # 清理数据
            cleaned_data = []
            for item in data_list:
                # 移除NaN值
                cleaned_item = {}
                for key, value in item.items():
                    if pd.notna(value):
                        cleaned_item[key] = value
                    else:
                        cleaned_item[key] = ""
                cleaned_data.append(cleaned_item)
            
            self.logger.info(f"成功导入 {len(cleaned_data)} 条数据")
            return cleaned_data
            
        except Exception as e:
            self.logger.error(f"导入Excel文件失败: {e}")
            messagebox.showerror("导入失败", f"无法导入Excel文件:\n{e}")
            return None
    
    def import_json_file(self, file_path: str) -> Optional[List[Dict[str, Any]]]:
        """导入JSON文件"""
        try:
            self.logger.info(f"开始导入JSON文件: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 确保数据是列表格式
            if isinstance(data, dict):
                # 如果是字典，尝试找到数据列表
                if 'results' in data:
                    data_list = data['results']
                elif 'data' in data:
                    data_list = data['data']
                else:
                    # 如果找不到，将字典转为单项列表
                    data_list = [data]
            elif isinstance(data, list):
                data_list = data
            else:
                raise ValueError("不支持的JSON数据格式")
            
            self.logger.info(f"成功导入 {len(data_list)} 条数据")
            return data_list
            
        except Exception as e:
            self.logger.error(f"导入JSON文件失败: {e}")
            messagebox.showerror("导入失败", f"无法导入JSON文件:\n{e}")
            return None
    
    def reanalyze_data(self, data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """重新分析导入的数据"""
        try:
            self.logger.info(f"开始重新分析 {len(data_list)} 条数据")
            
            reanalyzed_results = []
            failed_count = 0
            
            for i, item in enumerate(data_list):
                try:
                    # 使用当前配置重新计算蓝海指数
                    result = self.score_calculator.calculate_blue_ocean_score(item)
                    reanalyzed_results.append(result)
                    
                    # 进度提示
                    if (i + 1) % 10 == 0:
                        self.logger.info(f"已处理 {i + 1}/{len(data_list)} 条数据")
                        
                except Exception as e:
                    self.logger.warning(f"重新分析第 {i+1} 条数据失败: {e}")
                    # 保留原数据，但标记为分析失败
                    failed_item = item.copy()
                    failed_item['蓝海指数'] = 0
                    failed_item['评级'] = '❌ 分析失败'
                    failed_item['分析详情'] = [f"重新分析失败: {str(e)}"]
                    reanalyzed_results.append(failed_item)
                    failed_count += 1
            
            self.logger.info(f"重新分析完成: 成功 {len(reanalyzed_results) - failed_count} 条，失败 {failed_count} 条")
            
            if failed_count > 0:
                messagebox.showwarning("部分失败", f"重新分析完成，但有 {failed_count} 条数据分析失败")
            
            return reanalyzed_results
            
        except Exception as e:
            self.logger.error(f"重新分析数据失败: {e}")
            messagebox.showerror("分析失败", f"重新分析数据时出错:\n{e}")
            return []
    
    def batch_import_files(self, file_paths: List[str]) -> Optional[List[Dict[str, Any]]]:
        """批量导入多个文件"""
        try:
            self.logger.info(f"开始批量导入 {len(file_paths)} 个文件")
            
            all_data = []
            import_summary = {
                'total_files': len(file_paths),
                'success_files': 0,
                'failed_files': 0,
                'total_records': 0,
                'file_details': []
            }
            
            for i, file_path in enumerate(file_paths):
                try:
                    self.logger.info(f"导入文件 {i+1}/{len(file_paths)}: {os.path.basename(file_path)}")
                    
                    # 根据文件扩展名选择导入方法
                    if file_path.lower().endswith(('.xlsx', '.xls')):
                        file_data = self.import_excel_file(file_path)
                    elif file_path.lower().endswith('.json'):
                        file_data = self.import_json_file(file_path)
                    else:
                        self.logger.warning(f"不支持的文件格式: {file_path}")
                        import_summary['failed_files'] += 1
                        import_summary['file_details'].append({
                            'file': os.path.basename(file_path),
                            'status': '失败',
                            'reason': '不支持的文件格式',
                            'records': 0
                        })
                        continue
                    
                    if file_data:
                        # 为每条数据添加来源文件信息
                        for item in file_data:
                            item['数据来源'] = os.path.basename(file_path)
                            item['导入时间'] = pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
                        
                        all_data.extend(file_data)
                        import_summary['success_files'] += 1
                        import_summary['total_records'] += len(file_data)
                        import_summary['file_details'].append({
                            'file': os.path.basename(file_path),
                            'status': '成功',
                            'reason': '',
                            'records': len(file_data)
                        })
                        
                        self.logger.info(f"文件 {os.path.basename(file_path)} 导入成功: {len(file_data)} 条记录")
                    else:
                        import_summary['failed_files'] += 1
                        import_summary['file_details'].append({
                            'file': os.path.basename(file_path),
                            'status': '失败',
                            'reason': '文件读取失败',
                            'records': 0
                        })
                        
                except Exception as e:
                    self.logger.error(f"导入文件 {file_path} 失败: {e}")
                    import_summary['failed_files'] += 1
                    import_summary['file_details'].append({
                        'file': os.path.basename(file_path),
                        'status': '失败',
                        'reason': str(e),
                        'records': 0
                    })
            
            self.logger.info(f"批量导入完成: 成功 {import_summary['success_files']} 个文件，"
                           f"失败 {import_summary['failed_files']} 个文件，"
                           f"总计 {import_summary['total_records']} 条记录")
            
            # 显示导入摘要
            self.show_import_summary(import_summary)
            
            return all_data if all_data else None
            
        except Exception as e:
            self.logger.error(f"批量导入失败: {e}")
            messagebox.showerror("批量导入失败", f"批量导入文件时出错:\n{e}")
            return None
    
    def show_import_summary(self, summary: Dict[str, Any]):
        """显示导入摘要"""
        summary_text = f"""📊 批量导入摘要

总文件数: {summary['total_files']}
成功导入: {summary['success_files']} 个文件
导入失败: {summary['failed_files']} 个文件
总记录数: {summary['total_records']} 条

📋 文件详情:
"""
        
        for detail in summary['file_details']:
            status_icon = "✅" if detail['status'] == '成功' else "❌"
            summary_text += f"{status_icon} {detail['file']}: {detail['records']} 条记录"
            if detail['reason']:
                summary_text += f" ({detail['reason']})"
            summary_text += "\n"
        
        messagebox.showinfo("批量导入完成", summary_text)
    
    def merge_and_deduplicate_data(self, data_list: List[Dict[str, Any]], 
                                  dedupe_key: str = '产品名称') -> List[Dict[str, Any]]:
        """合并数据并去重"""
        try:
            self.logger.info(f"开始合并和去重 {len(data_list)} 条数据")
            
            # 使用字典来去重，保留最新的数据
            merged_data = {}
            duplicate_count = 0
            
            for item in data_list:
                key = item.get(dedupe_key, f"未知产品_{len(merged_data)}")
                
                if key in merged_data:
                    # 如果有重复，比较导入时间，保留最新的
                    existing_time = merged_data[key].get('导入时间', '1900-01-01 00:00:00')
                    current_time = item.get('导入时间', '1900-01-01 00:00:00')
                    
                    if current_time > existing_time:
                        merged_data[key] = item
                        duplicate_count += 1
                        self.logger.debug(f"更新重复产品: {key}")
                    else:
                        duplicate_count += 1
                        self.logger.debug(f"跳过旧数据: {key}")
                else:
                    merged_data[key] = item
            
            result = list(merged_data.values())
            
            self.logger.info(f"合并完成: 原始 {len(data_list)} 条，去重后 {len(result)} 条，"
                           f"重复 {duplicate_count} 条")
            
            if duplicate_count > 0:
                messagebox.showinfo("数据去重", 
                                  f"发现 {duplicate_count} 条重复数据\n"
                                  f"已自动保留最新版本\n"
                                  f"最终数据: {len(result)} 条")
            
            return result
            
        except Exception as e:
            self.logger.error(f"合并数据失败: {e}")
            messagebox.showerror("合并失败", f"合并数据时出错:\n{e}")
            return data_list
    
    def sort_by_blue_ocean_score(self, data_list: List[Dict[str, Any]], 
                                ascending: bool = False) -> List[Dict[str, Any]]:
        """按蓝海指数排序"""
        try:
            self.logger.info(f"按蓝海指数排序 {len(data_list)} 条数据")
            
            # 排序数据
            sorted_data = sorted(data_list, 
                               key=lambda x: x.get('蓝海指数', 0), 
                               reverse=not ascending)
            
            # 添加排名信息
            for i, item in enumerate(sorted_data):
                item['排名'] = i + 1
                item['排序时间'] = pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
            
            self.logger.info(f"排序完成，最高分: {sorted_data[0].get('蓝海指数', 0)}，"
                           f"最低分: {sorted_data[-1].get('蓝海指数', 0)}")
            
            return sorted_data
            
        except Exception as e:
            self.logger.error(f"排序失败: {e}")
            messagebox.showerror("排序失败", f"数据排序时出错:\n{e}")
            return data_list

    def generate_batch_analysis_report(self, data_list: List[Dict[str, Any]]) -> str:
        """生成批量分析报告"""
        try:
            if not data_list:
                return "❌ 没有数据可分析"

            # 统计信息
            total_count = len(data_list)

            # 按评级分组统计
            rating_stats = {}
            score_sum = 0

            # 按来源文件统计
            source_stats = {}

            for item in data_list:
                # 评级统计
                rating = item.get('评级', '未知')
                rating_stats[rating] = rating_stats.get(rating, 0) + 1

                # 分数统计
                score = item.get('蓝海指数', 0)
                score_sum += score

                # 来源统计
                source = item.get('数据来源', '未知来源')
                source_stats[source] = source_stats.get(source, 0) + 1

            avg_score = score_sum / total_count if total_count > 0 else 0

            # 生成报告
            report = f"""📊 批量导入分析报告
{'=' * 50}

📈 总体统计:
  总产品数: {total_count} 个
  平均蓝海指数: {avg_score:.1f} 分
  最高分产品: {data_list[0].get('产品名称', '未知')} ({data_list[0].get('蓝海指数', 0)} 分)
  最低分产品: {data_list[-1].get('产品名称', '未知')} ({data_list[-1].get('蓝海指数', 0)} 分)

🏆 评级分布:
"""

            for rating, count in sorted(rating_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_count) * 100
                report += f"  {rating}: {count} 个 ({percentage:.1f}%)\n"

            report += f"\n📁 数据来源分布:\n"
            for source, count in sorted(source_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_count) * 100
                report += f"  {source}: {count} 个 ({percentage:.1f}%)\n"

            # 显示前10名产品
            report += f"\n🥇 蓝海指数排行榜 (前10名):\n"
            for i, item in enumerate(data_list[:10]):
                rank_icon = ["🥇", "🥈", "🥉"][i] if i < 3 else f"{i+1}."
                report += f"  {rank_icon} {item.get('产品名称', '未知')}: {item.get('蓝海指数', 0)} 分 ({item.get('评级', '未知')})\n"

            if total_count > 10:
                report += f"  ... 还有 {total_count - 10} 个产品\n"

            return report

        except Exception as e:
            self.logger.error(f"生成批量分析报告失败: {e}")
            return f"❌ 生成报告失败: {str(e)}"

    def compare_data_changes(self, old_data: List[Dict[str, Any]],
                           new_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """对比两次数据的变化"""
        try:
            # 创建产品名称到数据的映射
            old_map = {item.get('产品名称', f'产品_{i}'): item for i, item in enumerate(old_data)}
            new_map = {item.get('产品名称', f'产品_{i}'): item for i, item in enumerate(new_data)}

            # 分析变化
            comparison = {
                '总体统计': {
                    '第一次数据量': len(old_data),
                    '第二次数据量': len(new_data),
                    '共同产品数': 0,
                    '新增产品数': 0,
                    '消失产品数': 0
                },
                '评分变化': [],
                '新增产品': [],
                '消失产品': []
            }

            # 找出共同产品、新增产品、消失产品
            old_names = set(old_map.keys())
            new_names = set(new_map.keys())

            common_names = old_names & new_names
            new_names_only = new_names - old_names
            old_names_only = old_names - new_names

            comparison['总体统计']['共同产品数'] = len(common_names)
            comparison['总体统计']['新增产品数'] = len(new_names_only)
            comparison['总体统计']['消失产品数'] = len(old_names_only)

            # 分析共同产品的评分变化
            for name in common_names:
                old_item = old_map[name]
                new_item = new_map[name]

                old_score = old_item.get('蓝海指数', 0)
                new_score = new_item.get('蓝海指数', 0)

                if old_score != new_score:
                    change = {
                        '产品名称': name,
                        '原评分': old_score,
                        '新评分': new_score,
                        '变化': new_score - old_score,
                        '原评级': old_item.get('评级', '未知'),
                        '新评级': new_item.get('评级', '未知')
                    }
                    comparison['评分变化'].append(change)

            # 记录新增和消失的产品
            for name in new_names_only:
                comparison['新增产品'].append({
                    '产品名称': name,
                    '蓝海指数': new_map[name].get('蓝海指数', 0),
                    '评级': new_map[name].get('评级', '未知')
                })

            for name in old_names_only:
                comparison['消失产品'].append({
                    '产品名称': name,
                    '蓝海指数': old_map[name].get('蓝海指数', 0),
                    '评级': old_map[name].get('评级', '未知')
                })

            return comparison

        except Exception as e:
            self.logger.error(f"对比数据变化失败: {e}")
            return {'错误': str(e)}

    def generate_comparison_report(self, comparison: Dict[str, Any]) -> str:
        """生成对比报告"""
        if '错误' in comparison:
            return f"❌ 对比分析失败: {comparison['错误']}"

        report = "📊 数据对比分析报告\n"
        report += "=" * 50 + "\n\n"

        # 总体统计
        stats = comparison['总体统计']
        report += "📈 总体统计:\n"
        report += f"  第一次数据: {stats['第一次数据量']} 个产品\n"
        report += f"  第二次数据: {stats['第二次数据量']} 个产品\n"
        report += f"  共同产品: {stats['共同产品数']} 个\n"
        report += f"  新增产品: {stats['新增产品数']} 个\n"
        report += f"  消失产品: {stats['消失产品数']} 个\n\n"

        # 评分变化
        changes = comparison['评分变化']
        if changes:
            report += f"🔄 评分变化 ({len(changes)} 个产品):\n"
            for change in changes[:10]:  # 只显示前10个
                direction = "📈" if change['变化'] > 0 else "📉"
                report += f"  {direction} {change['产品名称']}: {change['原评分']} → {change['新评分']} ({change['变化']:+.1f})\n"
            if len(changes) > 10:
                report += f"  ... 还有 {len(changes) - 10} 个产品有变化\n"
        else:
            report += "🔄 评分变化: 无变化\n"

        report += "\n"

        # 新增产品
        new_products = comparison['新增产品']
        if new_products:
            report += f"🆕 新增产品 ({len(new_products)} 个):\n"
            for product in new_products[:5]:  # 只显示前5个
                report += f"  • {product['产品名称']}: {product['蓝海指数']}分 ({product['评级']})\n"
            if len(new_products) > 5:
                report += f"  ... 还有 {len(new_products) - 5} 个新产品\n"
        else:
            report += "🆕 新增产品: 无\n"

        report += "\n"

        # 消失产品
        disappeared = comparison['消失产品']
        if disappeared:
            report += f"❌ 消失产品 ({len(disappeared)} 个):\n"
            for product in disappeared[:5]:  # 只显示前5个
                report += f"  • {product['产品名称']}: {product['蓝海指数']}分 ({product['评级']})\n"
            if len(disappeared) > 5:
                report += f"  ... 还有 {len(disappeared) - 5} 个消失的产品\n"
        else:
            report += "❌ 消失产品: 无\n"

        return report
