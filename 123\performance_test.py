#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蓝海产品分析器 v3.0 性能测试脚本
对比新旧版本的性能差异
"""

import os
import time
import json
import logging
from pathlib import Path
from typing import Dict, List, Any
import pandas as pd

class PerformanceTest:
    """性能测试类"""
    
    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        self.test_results = []
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('performance_test.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def prepare_test_images(self, test_dir: str = "test_images") -> List[str]:
        """准备测试图片"""
        test_path = Path(test_dir)
        if not test_path.exists():
            self.logger.warning(f"测试目录不存在: {test_dir}")
            return []
        
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(test_path.glob(f"*{ext}"))
            image_files.extend(test_path.glob(f"*{ext.upper()}"))
        
        image_files = [str(f) for f in image_files]
        self.logger.info(f"找到 {len(image_files)} 张测试图片")
        return image_files
    
    def test_ocr_performance(self, image_files: List[str]) -> Dict[str, Any]:
        """测试OCR性能"""
        from config_manager import ConfigManager
        from ocr_processor import OCRProcessor
        
        self.logger.info("开始OCR性能测试...")
        
        # 初始化OCR处理器
        config_manager = ConfigManager()
        ocr_processor = OCRProcessor(config_manager)
        
        # 测试结果
        results = {
            'total_images': len(image_files),
            'successful_images': 0,
            'total_products': 0,
            'total_time': 0,
            'avg_time_per_image': 0,
            'gpu_enabled': False,
            'errors': []
        }
        
        start_time = time.time()
        
        for i, image_path in enumerate(image_files):
            try:
                self.logger.info(f"处理图片 {i+1}/{len(image_files)}: {os.path.basename(image_path)}")
                
                image_start = time.time()
                products_data = ocr_processor.process_image(image_path)
                image_time = time.time() - image_start
                
                if products_data:
                    results['successful_images'] += 1
                    results['total_products'] += len(products_data)
                    self.logger.info(f"  识别到 {len(products_data)} 个产品，耗时 {image_time:.2f}s")
                else:
                    self.logger.warning(f"  未识别到产品数据")
                    
            except Exception as e:
                error_msg = f"处理图片失败 {image_path}: {e}"
                results['errors'].append(error_msg)
                self.logger.error(error_msg)
        
        results['total_time'] = time.time() - start_time
        results['avg_time_per_image'] = results['total_time'] / len(image_files) if image_files else 0
        
        # 获取OCR统计信息
        ocr_stats = ocr_processor.get_performance_stats()
        results['gpu_enabled'] = ocr_stats.get('gpu_enabled', False)
        
        return results
    
    def test_batch_processing(self, image_files: List[str]) -> Dict[str, Any]:
        """测试批处理性能"""
        from config_manager import ConfigManager
        from ocr_processor import OCRProcessor
        
        self.logger.info("开始批处理性能测试...")
        
        # 初始化OCR处理器
        config_manager = ConfigManager()
        ocr_processor = OCRProcessor(config_manager)
        
        results = {
            'total_images': len(image_files),
            'total_products': 0,
            'total_time': 0,
            'gpu_enabled': False,
            'batch_size': 5
        }
        
        start_time = time.time()
        
        try:
            # 使用批处理
            all_products_data = ocr_processor.process_images_batch(image_files)
            results['total_products'] = len(all_products_data)
            
        except Exception as e:
            self.logger.error(f"批处理失败: {e}")
            results['error'] = str(e)
        
        results['total_time'] = time.time() - start_time
        
        # 获取OCR统计信息
        ocr_stats = ocr_processor.get_performance_stats()
        results['gpu_enabled'] = ocr_stats.get('gpu_enabled', False)
        
        return results
    
    def compare_performance(self, sequential_results: Dict, batch_results: Dict) -> Dict[str, Any]:
        """对比性能结果"""
        comparison = {
            'sequential': sequential_results,
            'batch': batch_results,
            'improvement': {}
        }
        
        # 计算性能提升
        if sequential_results['total_time'] > 0:
            time_improvement = (sequential_results['total_time'] - batch_results['total_time']) / sequential_results['total_time'] * 100
            comparison['improvement']['time_saved_percent'] = time_improvement
            comparison['improvement']['speedup_factor'] = sequential_results['total_time'] / batch_results['total_time']
        
        # 计算准确性对比
        seq_success_rate = sequential_results['successful_images'] / sequential_results['total_images'] * 100
        comparison['improvement']['sequential_success_rate'] = seq_success_rate
        
        return comparison
    
    def generate_report(self, comparison: Dict[str, Any]) -> str:
        """生成性能报告"""
        report = f"""
🚀 蓝海产品分析器 v3.0 性能测试报告
{'='*60}

📊 测试概况:
• 测试图片数量: {comparison['sequential']['total_images']}
• GPU加速状态: {'启用' if comparison['sequential']['gpu_enabled'] else '禁用'}

🔄 顺序处理模式:
• 成功处理: {comparison['sequential']['successful_images']}/{comparison['sequential']['total_images']}
• 识别产品: {comparison['sequential']['total_products']} 个
• 总耗时: {comparison['sequential']['total_time']:.2f} 秒
• 平均每张: {comparison['sequential']['avg_time_per_image']:.2f} 秒
• 成功率: {comparison['improvement']['sequential_success_rate']:.1f}%

⚡ 批处理模式:
• 识别产品: {comparison['batch']['total_products']} 个
• 总耗时: {comparison['batch']['total_time']:.2f} 秒
• 批处理大小: {comparison['batch']['batch_size']}

🏆 性能提升:
• 时间节省: {comparison['improvement'].get('time_saved_percent', 0):.1f}%
• 加速倍数: {comparison['improvement'].get('speedup_factor', 1):.2f}x

💡 建议:
"""
        
        if comparison['sequential']['gpu_enabled']:
            if comparison['improvement'].get('speedup_factor', 1) > 2:
                report += "• ✅ GPU加速效果显著，建议使用批处理模式\n"
            else:
                report += "• ⚠️ GPU加速效果一般，可能受图片复杂度影响\n"
        else:
            report += "• 🔧 建议启用GPU加速以获得更好性能\n"
        
        if comparison['sequential']['total_images'] < 5:
            report += "• 📈 建议使用更多测试图片以获得更准确的性能数据\n"
        
        return report
    
    def run_full_test(self, test_dir: str = "test_images") -> None:
        """运行完整性能测试"""
        self.logger.info("开始完整性能测试...")
        
        # 准备测试图片
        image_files = self.prepare_test_images(test_dir)
        if not image_files:
            self.logger.error("没有找到测试图片，测试终止")
            return
        
        # 限制测试图片数量（避免测试时间过长）
        if len(image_files) > 10:
            image_files = image_files[:10]
            self.logger.info(f"限制测试图片数量为 {len(image_files)} 张")
        
        # 测试顺序处理
        sequential_results = self.test_ocr_performance(image_files)
        
        # 测试批处理
        batch_results = self.test_batch_processing(image_files)
        
        # 对比结果
        comparison = self.compare_performance(sequential_results, batch_results)
        
        # 生成报告
        report = self.generate_report(comparison)
        
        # 保存结果
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        results_file = f"performance_test_results_{timestamp}.json"
        report_file = f"performance_test_report_{timestamp}.txt"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(comparison, f, ensure_ascii=False, indent=2)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        # 显示报告
        print(report)
        
        self.logger.info(f"测试完成，结果已保存到: {results_file}, {report_file}")

def main():
    """主函数"""
    print("🧪 蓝海产品分析器 v3.0 性能测试")
    print("="*50)
    
    tester = PerformanceTest()
    
    # 检查测试目录
    test_dir = input("请输入测试图片目录路径 (默认: test_images): ").strip()
    if not test_dir:
        test_dir = "test_images"
    
    if not os.path.exists(test_dir):
        print(f"❌ 测试目录不存在: {test_dir}")
        print("请将测试图片放入指定目录后重新运行")
        return
    
    # 运行测试
    tester.run_full_test(test_dir)

if __name__ == "__main__":
    main()
