@echo off
echo Starting Blue Ocean Analyzer...

:: Switch to F drive (mounted drive)
cd /d F:
if %errorlevel% neq 0 (
    echo Error: Cannot switch to F drive
    pause
    exit /b 1
)

:: Navigate to project directory
cd "f:\zuomianwenjian\4.0\123"
if %errorlevel% neq 0 (
    echo Error: Cannot find project directory
    pause
    exit /b 1
)

:: Activate virtual environment
call "C:\Users\<USER>\lanhai\Scripts\activate.bat"
if %errorlevel% neq 0 (
    echo Error: Cannot activate virtual environment
    echo Check path: C:\Users\<USER>\lanhai
    pause
    exit /b 1
)

:: Start the program
echo Starting Blue Ocean Analyzer v3.0...
python blue_ocean_analyzer_v2.py

:: Keep window open
pause
