#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评分配置编辑器
可视化编辑评分系统的各种参数
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
from config_manager import ConfigManager

class ScoringConfigEditor:
    """评分配置编辑器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.root = tk.Tk()
        self.root.title("蓝海产品分析器 - 评分配置编辑器")
        self.root.geometry("800x600")
        
        # 存储所有输入控件的引用
        self.entries = {}
        
        self.setup_gui()
        self.load_current_config()
    
    def setup_gui(self):
        """设置GUI界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建滚动框架
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 标题
        title_label = ttk.Label(scrollable_frame, text="🎯 评分系统配置", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        row = 1
        
        # 基础评分配置
        row = self.create_section(scrollable_frame, "📊 基础评分配置", row)
        
        # 需求供给比
        row = self.create_subsection(scrollable_frame, "需求供给比评分", row)
        row = self.create_entry(scrollable_frame, "优秀分数", "基础评分.需求供给比.优秀_分数", row)
        row = self.create_entry(scrollable_frame, "良好分数", "基础评分.需求供给比.良好_分数", row)
        row = self.create_entry(scrollable_frame, "一般分数", "基础评分.需求供给比.一般_分数", row)
        row = self.create_entry(scrollable_frame, "一般阈值", "基础评分.需求供给比.一般_阈值", row)
        
        # 转化率
        row = self.create_subsection(scrollable_frame, "转化率评分", row)
        row = self.create_entry(scrollable_frame, "优秀分数", "基础评分.转化率.优秀_分数", row)
        row = self.create_entry(scrollable_frame, "良好分数", "基础评分.转化率.良好_分数", row)
        row = self.create_entry(scrollable_frame, "一般分数", "基础评分.转化率.一般_分数", row)
        row = self.create_entry(scrollable_frame, "一般阈值", "基础评分.转化率.一般_阈值", row)
        
        # 天猫占比
        row = self.create_subsection(scrollable_frame, "天猫占比评分", row)
        row = self.create_entry(scrollable_frame, "优秀分数", "基础评分.天猫占比.低_分数", row)
        row = self.create_entry(scrollable_frame, "良好分数", "基础评分.天猫占比.中_分数", row)
        row = self.create_entry(scrollable_frame, "高占比分数", "基础评分.天猫占比.高_分数", row)
        
        # 搜索人气
        row = self.create_subsection(scrollable_frame, "搜索人气评分", row)
        row = self.create_entry(scrollable_frame, "很高分数", "基础评分.搜索人气.很高_分数", row)
        row = self.create_entry(scrollable_frame, "很高阈值", "基础评分.搜索人气.很高_阈值", row)
        row = self.create_entry(scrollable_frame, "较高分数", "基础评分.搜索人气.较高_分数", row)
        row = self.create_entry(scrollable_frame, "较高阈值", "基础评分.搜索人气.较高_阈值", row)
        row = self.create_entry(scrollable_frame, "中等分数", "基础评分.搜索人气.中等_分数", row)
        row = self.create_entry(scrollable_frame, "中等阈值", "基础评分.搜索人气.中等_阈值", row)
        row = self.create_entry(scrollable_frame, "较低分数", "基础评分.搜索人气.较低_分数", row)
        
        # 趋势加分配置
        row = self.create_section(scrollable_frame, "📈 趋势加分配置", row)
        
        # 评级标准
        row = self.create_section(scrollable_frame, "🏆 评级标准", row)
        row = self.create_entry(scrollable_frame, "蓝海产品最低分", "评级标准.蓝海产品_最低分", row)
        row = self.create_entry(scrollable_frame, "潜力产品最低分", "评级标准.潜力产品_最低分", row)
        row = self.create_entry(scrollable_frame, "一般产品最低分", "评级标准.一般产品_最低分", row)
        
        # 按钮框架
        button_frame = ttk.Frame(scrollable_frame)
        button_frame.grid(row=row, column=0, columnspan=3, pady=20)
        
        ttk.Button(button_frame, text="💾 保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔄 重置为默认", command=self.reset_to_default).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="❌ 关闭", command=self.root.destroy).pack(side=tk.LEFT, padx=5)
        
        # 配置滚动
        canvas.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def create_section(self, parent, title, row):
        """创建章节标题"""
        ttk.Label(parent, text=title, font=("Arial", 12, "bold")).grid(
            row=row, column=0, columnspan=3, sticky=tk.W, pady=(20, 5)
        )
        return row + 1
    
    def create_subsection(self, parent, title, row):
        """创建子章节标题"""
        ttk.Label(parent, text=f"  {title}", font=("Arial", 10, "bold")).grid(
            row=row, column=0, columnspan=3, sticky=tk.W, pady=(10, 5)
        )
        return row + 1
    
    def create_entry(self, parent, label, config_key, row):
        """创建输入框"""
        ttk.Label(parent, text=f"    {label}:").grid(row=row, column=0, sticky=tk.W, padx=(20, 10))
        
        entry = ttk.Entry(parent, width=15)
        entry.grid(row=row, column=1, sticky=tk.W, padx=5)
        
        self.entries[config_key] = entry
        return row + 1
    
    def load_current_config(self):
        """加载当前配置"""
        scoring_config = self.config_manager.get_scoring_config()
        
        for key, entry in self.entries.items():
            value = self.get_nested_value(scoring_config, key)
            if value is not None:
                entry.delete(0, tk.END)
                entry.insert(0, str(value))
    
    def get_nested_value(self, config, key_path):
        """获取嵌套配置值"""
        keys = key_path.split('.')
        value = config
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None
        return value
    
    def save_config(self):
        """保存配置"""
        try:
            # 获取当前配置
            current_config = self.config_manager._config.copy()
            
            # 更新评分配置
            for key, entry in self.entries.items():
                value_str = entry.get().strip()
                if value_str:
                    # 尝试转换为数字
                    try:
                        value = float(value_str) if '.' in value_str else int(value_str)
                    except ValueError:
                        value = value_str
                    
                    # 设置嵌套值
                    self.set_nested_value(current_config, f"scoring_config.{key}", value)
            
            # 保存到文件
            with open(self.config_manager.config_path, 'w', encoding='utf-8') as f:
                json.dump(current_config, f, ensure_ascii=False, indent=4)
            
            # 重新加载配置
            self.config_manager._config = self.config_manager.load_config()
            
            messagebox.showinfo("成功", "配置已保存！")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败：{e}")
    
    def set_nested_value(self, config, key_path, value):
        """设置嵌套配置值"""
        keys = key_path.split('.')
        current = config
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
    
    def reset_to_default(self):
        """重置为默认配置"""
        if messagebox.askyesno("确认", "确定要重置为默认配置吗？"):
            default_config = self.config_manager.get_default_scoring_config()
            
            for key, entry in self.entries.items():
                value = self.get_nested_value(default_config, key)
                if value is not None:
                    entry.delete(0, tk.END)
                    entry.insert(0, str(value))
    
    def run(self):
        """运行编辑器"""
        self.root.mainloop()

if __name__ == "__main__":
    editor = ScoringConfigEditor()
    editor.run()
