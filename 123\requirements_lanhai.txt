# 蓝海产品分析器 v3.0 - 依赖包列表 (升级版)
# 使用方法: pip install -r requirements_lanhai.txt

# 核心OCR依赖 - 升级到最新版本
paddleocr==2.8.1
paddlepaddle==3.0.0

# 图像处理依赖 - 优化版本
pillow>=10.0.0
opencv-python>=4.8.0
numpy>=1.24.0,<2.0.0  # 限制numpy版本避免兼容性问题

# 数据处理依赖
pandas>=2.0.0
openpyxl>=3.1.0

# 性能优化依赖
shapely>=2.0.0  # PaddleOCR 2.8.1 推荐
pyclipper>=1.3.0  # 文本检测优化

# GUI界面依赖 (Python内置，无需安装)
# tkinter - Python内置库

# 可选：GPU加速支持 (如果有NVIDIA GPU)
# paddlepaddle-gpu==3.0.0  # 取消注释以启用GPU支持

# 开发和调试工具 (可选)
# pytest>=7.0.0
# black>=22.0.0

# 版本说明:
# PaddlePaddle 3.0.0: 性能提升20-30%，全新推理引擎
# PaddleOCR 2.8.1: 表格识别精度提升25%，版面分析算法升级
