创建lanhai虚拟环境   虚拟环境地址在C:\Users\<USER>\lanhai
python -m venv lanhai

进入激活
cd lanhai
Scripts\activate

验证
python -V
pip list

换pip源
pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/

升级一下pip
python -m pip install --upgrade pip



官方源安装
python -m pip install paddlepaddle-gpu==3.1.0 -i https://www.paddlepaddle.org.cn/packages/stable/cu118/
pip install paddleocr==3.1.0


# 图像处理依赖
pip install opencv-python==******** -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install opencv-contrib-python==******** -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install Pillow==10.0.1 -i https://pypi.tuna.tsinghua.edu.cn/simple

# 科学计算依赖
pip install numpy==1.24.3 -i https://pypi.tuna.tsinghua.edu.cn/simple

# 卸载当前numpy并安装兼容版本
pip uninstall numpy -y
pip install numpy==1.26.4 -i https://pypi.tuna.tsinghua.edu.cn/simple

pip install scipy==1.11.4 -i https://pypi.tuna.tsinghua.edu.cn/simple

# 文本处理依赖
pip install shapely==2.0.2 -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install pyclipper==1.3.0.post5 -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install imgaug==0.4.0 -i https://pypi.tuna.tsinghua.edu.cn/simple

# 可视化依赖
pip install matplotlib==3.7.2 -i https://pypi.tuna.tsinghua.edu.cn/simple

# 其他必要依赖
pip install lmdb==1.4.1 -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install tqdm==4.66.1 -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install rapidfuzz==3.5.2 -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install openpyxl==3.1.2 -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install attrdict==2.0.1 -i https://pypi.tuna.tsinghua.edu.cn/simple


注意模型切换
Server模型: 84MB + 81MB = 165MB (高精度，慢速度)
📱 Mobile模型: 5MB + 16MB = 21MB (中等精度，快速度)



rmdir /s /q C:\你的路径\lanhai（删除虚拟环境目录）

cd /d F:\zuomianwenjian\2.0\1111

python 启动分析器.py


配置	环境	模型版本	推理时间	加速比
CPU	lanhai	OCRv5 Server	91.31秒	基准
CPU	lanhai	OCRv4 Mobile	7.60秒	12倍
GPU	lanhaigao	OCRv4 Mobile	0.25-1.68秒	54-365倍